<!--药库-库房业务-批号库存查询-->
<script setup lang="ts">
import type { PaginationProps, TableProps } from 'ant-design-vue'
import { pageApi, downloadExcelApi } from '~/api/clinics_wm/deptstock.ts'
import { listApi as findAllDeptLsApi,splitPackApi } from "~/api/clinics_wm/deptcustmap";
import help from "~/utils/help";
// import {findStockEnabledAllApi} from "~/api/hip/cattype.ts";
import BatchExpiryUpdate from '@/pages/clinics-wm/comp/batch-expiry-update.vue'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
import { Modal, message } from 'ant-design-vue'


const userStore = useUserStore()
// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[],
  pagination: any
}
const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品名',
    dataIndex: 'artName',
    width: 180,
    ellipsis: true,
    align: 'left',
    fixed: 'left'
  }, {
    title: '规格',
    dataIndex: 'artSpec',
    ellipsis: true,
    width: 150,
    align: 'left'
  }, {
    title: '厂家',
    dataIndex: 'producer',
    ellipsis: true,
    width: 250,
    align: 'left'
  }, {
    title: '整包单位',
    dataIndex: 'packUnit',
    width: 120,
    align: 'center'
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 120,
    align: 'right'
  }, {
    title: '整包成本单价',
    dataIndex: 'packPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零成本单价',
    dataIndex: 'cellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '加成比例',
    dataIndex: 'pctAdd',
    width: 120,
    align: 'right'
  }, {
    title: '整包单销售价',
    dataIndex: 'salePackPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零销售单价',
    dataIndex: 'saleCellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    width: 120,
    align: 'right'
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 120,
    align: 'right'
  }, {
    title: '临期天数',
    dataIndex: 'expiryDays',
    width: 120,
    align: 'right'
  }, {
    title: '批准文号',
    dataIndex: 'approvalNo',
    width: 180,
    align: 'left'
  }, {
    title: '允许拆零',
    dataIndex: 'splittable',
    width: 80,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      return text ? text === 1 ? '是' : '' : ''
    },
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 240,
    align: 'left'
  }, {
    title: '包装材质',
    dataIndex: 'packMaterial',
    width: 150,
    ellipsis: true,
    align: 'left'
  }, {
    title: '剂型名称',
    dataIndex: 'dosageForm',
    width: 120,
    align: 'left'
  // }, {
  //   title: '包装单位',
  //   dataIndex: 'packUnit',
  //   width: 120,
  //   align: 'left'
  // }, {
  //   title: '制剂单位',
  //   dataIndex: 'cellUnit',
  //   width: 120,
  //   align: 'left'
  }, {
    title: '货位编码',
    dataIndex: 'rackNo',
    width: 90,
    align: 'center'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 120,
    align: 'right'
  }, {
    title: '成本总额',
    dataIndex: 'costAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '销售总额',
    dataIndex: 'saleAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '仓库总库存',
    dataIndex: 'deptTotalPacks',
    width: 100,
    align: 'right',
    fixed: 'right'
  }, {
    title: '批次库存',
    dataIndex: 'currentTotal',
    width: 100,
    align: 'right',
    fixed: 'right'
  },{
    title: '操作',
    dataIndex: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }],
  dataSource: [],
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const {data} = await pageApi({
        ...searchFormModel,
        sidx: 't_article.Art_ID, t_dept_stock.stock_no',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0
      tableModel.sumRow = data.extra
      tableModel.loading = false
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${ tableModel.pagination.total } 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<any>({
})

const onReset = async () => {
  searchFormModel.S_EQ_t_article__art_id = undefined
  searchFormModel.expiryDays = undefined
  searchFormModel.keyword = undefined
  searchFormModel.S_EQ_t_article__cat_type_id = undefined
  searchFormModel.S_EQ_t_article__Subtype_ID = undefined
  searchFormModel.cellsTotalLe = undefined
  await onSearch()
}
const onSearch = async () => {
  tableModel.pagination.current = 1
  tableModel.loadDataSource()
}
const deptLs = ref<any[]>([])
const onChangeDept = () => {
  onSearch()
}
const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isOperator: 1
  })
  deptLs.value = data
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode
    onChangeDept()
  }
}
// const catTypeList = ref([])
// const getCatTypeLs = async () => {
//   const { data } = await findStockEnabledAllApi({})
//   catTypeList.value = data
// }
onMounted(async () => {
  await getDeptLs()
  // await getCatTypeLs()
  await onSearch()
})
const onDownloadExcel = async () => {
  const res: any = await downloadExcelApi({
    ...searchFormModel
  })
  help.expExcel(res, '库存明细数据')
}
const batchExpiryUpdateVisible = ref(false)
const batchExpiryUpdateRef = ref()
const handleEditBatch = (record) => {
  batchExpiryUpdateVisible.value = true
  batchExpiryUpdateRef.value?.init(record)
}

// 拆零盒整相关状态
const splitPackModalVisible = ref(false)
const currentSplitRecord = ref<any>(null)
const splitPackForm = reactive({
  deptMaxPacks: 0,    // 仓库总库存能合并的最大包装数
  batchMaxPacks: 0,   // 批次库存能合并的最大包装数
  deptInputPacks: 0,  // 用户输入的仓库整包数
  batchInputPacks: 0, // 用户输入的批次整包数
  deptInputCells: 0,  // 用户输入的仓库拆零数
  batchInputCells: 0, // 用户输入的批次拆零数
  deptMaxCells: 0,    // 仓库总库存最大拆零数
  batchMaxCells: 0,   // 批次库存最大拆零数
})

// 计算仓库总库存最大拆零数
const getDeptMaxCells = () => {
  if (!currentSplitRecord.value) return 0
  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  return deptTotalPacks * packCells + deptTotalCells
}

// 计算批次库存最大拆零数
const getBatchMaxCells = () => {
  if (!currentSplitRecord.value) return 0
  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  return batchTotalPacks * packCells + batchTotalCells
}

// 判断是否有有效的拆零单位（是否允许拆零输入）
const hasValidCellUnit = () => {
  if (!currentSplitRecord.value) return false

  const packCells = currentSplitRecord.value.packCells || 1
  const cellUnit = currentSplitRecord.value.cellUnit || ''
  const packUnit = currentSplitRecord.value.packUnit || ''

  // 如果包装规格大于1，且拆零单位与整包单位不同，则允许拆零输入
  // 例如：10支/盒 (packCells=10, cellUnit=支, packUnit=盒) ✅
  // 例如：1袋/袋 (packCells=1, cellUnit=袋, packUnit=袋) ❌
  return packCells > 1 && cellUnit !== packUnit && cellUnit.trim() !== ''
}

// 计算能合并的最大包装数和拆零数
const calculateMaxPackCount = (record: any) => {
  const packCells = record.packCells || 1

  // 计算仓库总库存
  const deptTotalPacks = record.deptTotalPacks || 0
  const deptTotalCells = record.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 仓库总库存的最大包装数和剩余拆零数
  splitPackForm.deptMaxPacks = Math.floor(deptTotalInCells / packCells)
  splitPackForm.deptMaxCells = deptTotalInCells % packCells

  // 计算批次库存
  const batchTotalPacks = record.totalPacks || 0
  const batchTotalCells = record.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 批次库存的最大包装数和剩余拆零数
  splitPackForm.batchMaxPacks = Math.floor(batchTotalInCells / packCells)
  splitPackForm.batchMaxCells = batchTotalInCells % packCells

  // 默认设置为原始数据（record传输的数据）
  splitPackForm.deptInputPacks = deptTotalPacks
  splitPackForm.deptInputCells = deptTotalCells

  // 批次库存整包数自动计算最大值并赋值
  const batchMaxPacksCalculated = Math.floor(batchTotalInCells / packCells)
  splitPackForm.batchInputPacks = batchMaxPacksCalculated
  splitPackForm.batchInputCells = batchTotalInCells % packCells
}

// 仓库总库存整包数变化时的联动计算
const handleDeptPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = deptTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.deptInputCells = remainingCells
  }
}

// 仓库总库存拆零数变化时的联动计算
const handleDeptCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > deptTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.deptInputCells = deptTotalInCells
    splitPackForm.deptInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.deptInputCells = 0
    splitPackForm.deptInputPacks = Math.floor(deptTotalInCells / packCells)
    return
  }

  // 根据拆零数和包装规格自动计算整包数
  // 计算公式：整包数 = Math.floor(拆零数 / 包装规格)
  // 剩余拆零数 = 拆零数 % 包装规格
  const calculatedPacks = Math.floor(numValue / packCells)
  const remainingCells = numValue % packCells

  // 验证计算结果不超过原始总库存
  const totalCalculated = calculatedPacks * packCells + remainingCells
  if (totalCalculated <= deptTotalInCells) {
    splitPackForm.deptInputPacks = calculatedPacks
    splitPackForm.deptInputCells = remainingCells
  } else {
    // 如果超出，则按最大可能分配
    const maxPacks = Math.floor(deptTotalInCells / packCells)
    const maxCells = deptTotalInCells % packCells
    splitPackForm.deptInputPacks = maxPacks
    splitPackForm.deptInputCells = maxCells
  }
}

// 批次库存整包数变化时的联动计算
const handleBatchPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = batchTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.batchInputCells = remainingCells
  }
}

// 批次库存拆零数变化时的联动计算
const handleBatchCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > batchTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.batchInputCells = batchTotalInCells
    splitPackForm.batchInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.batchInputCells = 0
    splitPackForm.batchInputPacks = Math.floor(batchTotalInCells / packCells)
    return
  }

  // 根据拆零数和包装规格自动计算整包数
  // 计算公式：整包数 = Math.floor(拆零数 / 包装规格)
  // 剩余拆零数 = 拆零数 % 包装规格
  const calculatedPacks = Math.floor(numValue / packCells)
  const remainingCells = numValue % packCells

  // 验证计算结果不超过原始总库存
  const totalCalculated = calculatedPacks * packCells + remainingCells
  if (totalCalculated <= batchTotalInCells) {
    splitPackForm.batchInputPacks = calculatedPacks
    splitPackForm.batchInputCells = remainingCells
  } else {
    // 如果超出，则按最大可能分配
    const maxPacks = Math.floor(batchTotalInCells / packCells)
    const maxCells = batchTotalInCells % packCells
    splitPackForm.batchInputPacks = maxPacks
    splitPackForm.batchInputCells = maxCells
  }
}

// 拆零盒整方法
const handleSplitPack = (record) => {
  // 打开拆零盒整录入页面
  splitPackModalVisible.value = true
  currentSplitRecord.value = record

  // 计算能合并的最大包装数
  calculateMaxPackCount(record)
}

// 校验整包数与拆零数的准确性
const validateSplitPackData = () => {
  if (!currentSplitRecord.value) {
    message.error('数据异常，请重新操作')
    return false
  }

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0

  // 计算原始总库存（转换为拆零数）
  const batchOriginalTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 计算用户输入的总库存（转换为拆零数）
  const batchInputTotalInCells = splitPackForm.batchInputPacks * packCells + splitPackForm.batchInputCells

  // 仓库总库存不可修改，无需校验

  // 校验批次库存
  if (batchInputTotalInCells !== batchOriginalTotalInCells) {
    message.error(`批次库存数据不匹配！
原始：${batchTotalPacks}${currentSplitRecord.value.packUnit}${batchTotalCells}${currentSplitRecord.value.cellUnit} (共${batchOriginalTotalInCells}${currentSplitRecord.value.cellUnit})
输入：${splitPackForm.batchInputPacks}${currentSplitRecord.value.packUnit}${splitPackForm.batchInputCells}${currentSplitRecord.value.cellUnit} (共${batchInputTotalInCells}${currentSplitRecord.value.cellUnit})`)
    return false
  }

  // 校验整包数不能为负数
  if (splitPackForm.deptInputPacks < 0 || splitPackForm.batchInputPacks < 0) {
    message.error('整包数不能为负数')
    return false
  }

  // 校验批次库存整包数不能小于原始值
  const originalBatchPacks = currentSplitRecord.value.totalPacks || 0
  if (splitPackForm.batchInputPacks < originalBatchPacks) {
    message.error(`批次库存整包数不能小于原始值(${originalBatchPacks}${currentSplitRecord.value.packUnit})`)
    return false
  }

  // 校验拆零数不能为负数
  if (splitPackForm.deptInputCells < 0 || splitPackForm.batchInputCells < 0) {
    message.error('拆零数不能为负数')
    return false
  }

  // 注释：允许拆零数大于包装规格
  // 例如：包装规格10片/盒，允许输入15片、25片等
  // 系统会自动处理转换逻辑

  return true
}

// 获取实时校验状态（用于界面显示）
const getValidationStatus = () => {
  if (!currentSplitRecord.value) return { isValid: false, message: '' }

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0

  // 计算原始批次库存总量
  const batchOriginalTotal = batchTotalPacks * packCells + batchTotalCells

  // 计算输入批次库存总量
  const batchInputTotal = splitPackForm.batchInputPacks * packCells + splitPackForm.batchInputCells

  // 检查批次库存数据匹配性
  const batchMatched = batchInputTotal === batchOriginalTotal

  if (!batchMatched) {
    return {
      isValid: false,
      message: `批次库存不匹配：输入${batchInputTotal}${currentSplitRecord.value.cellUnit}，应为${batchOriginalTotal}${currentSplitRecord.value.cellUnit}`
    }
  }

  return { isValid: true, message: '数据校验通过' }
}

// 确认拆零盒整操作
const handleSplitPackConfirm = () => {
  if (!currentSplitRecord.value) return

  // 校验数据准确性
  if (!validateSplitPackData()) {
    return
  }

  // 执行拆零盒整操作
  performSplitPackWithInput(currentSplitRecord.value)

  // 关闭模态框
  splitPackModalVisible.value = false
}

// 根据用户输入执行拆零盒整操作
const performSplitPackWithInput = async (record: any) => {
  try {
    // 准备传递给后台的数据
    const requestData = {
      ...record,
      // 更新后的仓库总库存
      deptTotalPacks: splitPackForm.deptInputPacks,
      deptTotalCells: splitPackForm.deptInputCells,
      // 更新后的批次库存
      totalPacks: splitPackForm.batchInputPacks,
      totalCells: splitPackForm.batchInputCells,
      // 添加其他必要的参数
      ...searchFormModel
    }

    console.log('拆零盒整操作开始，传递数据:', JSON.stringify(requestData, null, 2))

    // 调用后台接口，将requestData通过JSON格式传递
    const { data } = await splitPackApi({
      record: JSON.stringify(requestData)
    })

    console.log('拆零盒整操作完成，后台返回:', data)

    // 更新选中行的数据
    const index = tableModel.dataSource.findIndex(item =>
      item.artId === record.artId && item.stockNo === record.stockNo
    )

    if (index !== -1) {
      // 更新仓库总库存
      tableModel.dataSource[index].deptTotalPacks = splitPackForm.deptInputPacks
      tableModel.dataSource[index].deptTotalCells = splitPackForm.deptInputCells

      // 更新批次库存
      tableModel.dataSource[index].totalPacks = splitPackForm.batchInputPacks
      tableModel.dataSource[index].totalCells = splitPackForm.batchInputCells

      const deptDisplay = splitPackForm.deptInputPacks > 0 ?
        `${splitPackForm.deptInputPacks}${record.packUnit}` +
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '') :
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '0')

      const batchDisplay = splitPackForm.batchInputPacks > 0 ?
        `${splitPackForm.batchInputPacks}${record.packUnit}` +
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '') :
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '0')

      message.success(`拆零盒整完成！
仓库总库存：${deptDisplay}
批次库存：${batchDisplay}`)
    }
  } catch (error) {
    console.error('拆零盒整操作失败:', error)
    message.error('拆零盒整操作失败，请重试')
  }
}

// 取消拆零盒整操作
const handleSplitPackCancel = () => {
  splitPackModalVisible.value = false
  currentSplitRecord.value = null
}



const onCloseForm = () => {
  batchExpiryUpdateVisible.value = false
  tableModel.loadDataSource()
}
const tableSummary = computed(() => {
  if (!tableModel.sumRow) {
    console.log('数据未加载，跳过计算');
    return null;
  }
  return [{
    index: 0,
    colSpan: 1,
    label: '合计：',
    style: {}
  }, {
    index: 22,
    colSpan: 1,
    label: tableModel.sumRow.costAmount,
    style: {
      textAlign: 'right'
    }
  }, {
    index: 23,
    colSpan: 1,
    label: tableModel.sumRow.saleAmount,
    style: {
      textAlign: 'right'
    }
  }]
})
</script>
<template>
  <page-container>
    <div class="bg-fff box-shadow p-16px h-full">
      <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource" :rowKey="(item: any) => item.artId + '-' + item.stockNo"
                  :pagination="tableModel.pagination" :summary="tableSummary">
        <template #btns>
          <a-form :model="searchFormModel" w-full>
            <a-row p-t-2>
              <a-col flex="200px">
                <a-form-item label="仓库" name="deptCode">
                  <a-select v-model:value="searchFormModel.deptCode" placeholder="请选择仓库" style="min-width: 120px;" @change="onSearch">
                    <a-select-option v-for="item in deptLs" :key="item.deptCode" :value="item.deptCode">{{ item.deptName }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="临期天数">
                  <a-input-number v-model:value="searchFormModel.expiryDays" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="条目ID" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <a-input v-model:value="searchFormModel.S_EQ_t_article__art_id" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="250px">
                <a-form-item label="品名" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                  <a-input v-model:value="searchFormModel.keyword" allow-clear @pressEnter="onSearch"/>
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <StockReqCat v-model="searchFormModel.S_EQ_t_article__cat_type_id" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目亚类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <ArtSubTypeDict v-model="searchFormModel.S_EQ_t_article__Subtype_ID" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-button type="primary" :loading="tableModel.loading" @click="onSearch" m-l-2>
                  查询
                </a-button>
                <a-button @click="onReset" m-l-2>
                  重置
                </a-button>
              </a-col>
              <a-col flex="auto">
              </a-col>
              <a-col flex="100px">
                <a-button :loading="tableModel.loading" @click="onDownloadExcel">
                  导出
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
<!--          <template v-if="column?.dataIndex === 'artName'">-->
<!--            {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}-->
<!--          </template>-->
          <template v-if="column?.dataIndex === 'packUnit'">
            {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
          </template>
          <template v-if="column?.dataIndex === 'deptTotalPacks'">
            <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span><span v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'currentTotal'">
            <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'safeTotal'">
            <span v-if="record.safeTotalPacks">{{ record.safeTotalPacks }}{{ record.packUnit }}</span><span v-if="record.safeTotalCells">{{ record.safeTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'lastRestCells'">
            <span v-if="record.lastRestTotalPacks">{{ record.lastRestTotalPacks }}{{ record.packUnit }}</span><span v-if="record.lastRestTotalCells">{{ record.lastRestTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'action'">
             <a-space>
              <a @click="handleSplitPack(record)">拆零盒整</a>
            </a-space>
            &nbsp;
            <a-space>
              <a @click="handleEditBatch(record)">修改效期</a>
            </a-space>

          </template>
        </template>
      </base-table>
    </div>
    <batch-expiry-update ref="batchExpiryUpdateRef" v-model:visible="batchExpiryUpdateVisible" width="800px" @close="onCloseForm"/>

    <!-- 拆零盒整录入模态框 -->
    <a-modal
      v-model:open="splitPackModalVisible"
      title="拆零盒整录入"
      width="600px"
      :ok-button-props="{ disabled: !getValidationStatus().isValid }"
      @ok="handleSplitPackConfirm"
      @cancel="handleSplitPackCancel"
    >
      <div v-if="currentSplitRecord">
        <div class="mb-16px">
          <h4>品种信息</h4>
          <p><strong>品名：</strong>{{ currentSplitRecord.artName }}</p>
          <p><strong>规格：</strong>{{ currentSplitRecord.artSpec }}</p>
          <p><strong>生产厂家：</strong>{{ currentSplitRecord.producer }}</p>
          <p><strong>包装规格：</strong>{{ currentSplitRecord.packCells }}{{ currentSplitRecord.cellUnit }}/{{ currentSplitRecord.packUnit }}</p>
        </div>

        <a-divider />

        <a-form :model="splitPackForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="仓库总库存整包数">
                <a-input-number
                  v-model:value="splitPackForm.deptInputPacks"
                  :min="0"
                  :max="splitPackForm.deptMaxPacks"
                  :addon-after="currentSplitRecord.packUnit"
                  :disabled="true"
                  style="width: 100%"
                  @change="handleDeptPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  (原始：{{ currentSplitRecord.deptTotalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仓库总库存拆零数">
                <a-input-number
                  v-model:value="splitPackForm.deptInputCells"
                  :min="0"
                  :max="getDeptMaxCells()"
                  :addon-after="currentSplitRecord.cellUnit"
                  :disabled="true"
                  style="width: 100%"
                  @change="handleDeptCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                 
                  (原始：{{ currentSplitRecord.deptTotalCells || 0 }}{{ currentSplitRecord.cellUnit }})
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="批次库存整包数">
                <a-input-number
                  v-model:value="splitPackForm.batchInputPacks"
                  :min="currentSplitRecord.totalPacks || 0"
                  :max="Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1))"
                  :addon-after="currentSplitRecord.packUnit"
                  style="width: 100%"
                  @change="handleBatchPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  范围：{{ currentSplitRecord.totalPacks || 0 }}-{{ Math.floor(getBatchMaxCells() / (currentSplitRecord.packCells || 1)) }}{{ currentSplitRecord.packUnit }}
                  (原始：{{ currentSplitRecord.totalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="批次库存拆零数">
                <a-input-number
                  v-model:value="splitPackForm.batchInputCells"
                  :min="0"
                  :max="getBatchMaxCells()"
                  :addon-after="currentSplitRecord.cellUnit"
                  :disabled="!hasValidCellUnit()"
                  style="width: 100%"
                  @change="handleBatchCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  <span v-if="hasValidCellUnit()">
                    范围：0-{{ getBatchMaxCells() }}{{ currentSplitRecord.cellUnit }}
                    (原始：{{ currentSplitRecord.totalCells || 0 }}{{ currentSplitRecord.cellUnit }})
                  </span>
                  <span v-else class="text-orange-500">
                    整盒包装，无拆零单位 ({{ currentSplitRecord.packCells }}{{ currentSplitRecord.cellUnit }}/{{ currentSplitRecord.packUnit }})
                  </span>
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider />

          <!-- 实时校验状态显示 -->
          <div class="mb-16px">
            <a-alert
              :type="getValidationStatus().isValid ? 'success' : 'warning'"
              :message="getValidationStatus().message"
              show-icon
              :style="{ marginBottom: '16px' }"
            />
          </div>

          <div class="bg-gray-50 p-12px rounded">
            <h5>操作说明：</h5>
            <ul class="text-sm text-gray-600">
              <li>• <strong>输入限制：</strong></li>
              <li>&nbsp;&nbsp;- 仓库总库存整包数：不可修改</li>
              <li>&nbsp;&nbsp;- 仓库总库存拆零数：不可修改</li>
              <li>&nbsp;&nbsp;- 批次库存整包数：自动计算最大值并赋值，可手动调整</li>
              <li>&nbsp;&nbsp;- 批次库存拆零数：可修改，支持智能联动</li>
              <li>• <strong>自动计算：</strong></li>
              <li>&nbsp;&nbsp;- 打开页面时自动计算批次库存的最大整包数</li>
              <li>&nbsp;&nbsp;- 剩余拆零数自动计算为：总拆零数 % 包装规格</li>
              <li>• <strong>智能联动计算：</strong></li>
              <li>&nbsp;&nbsp;- 输入批次库存整包数 → 自动计算剩余拆零数</li>
              <li>• <strong>拆零数计算规则：</strong></li>
              <li>&nbsp;&nbsp;- 整包数 = Math.floor(拆零数 ÷ 包装规格)</li>
              <li>&nbsp;&nbsp;- 剩余拆零数 = 拆零数 % 包装规格</li>
              <li>• <strong>校验要求：</strong></li>
              <li>&nbsp;&nbsp;- 总量必须等于原始库存总量</li>
              <li>&nbsp;&nbsp;- 整包数和拆零数不能为负数</li>
              <li>• <strong>整盒包装：</strong>无拆零单位时禁用拆零数输入</li>
            </ul>
          </div>
        </a-form>
      </div>
    </a-modal>
  </page-container>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
