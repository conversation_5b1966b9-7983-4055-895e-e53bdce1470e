<!--药库-库房业务-批号库存查询-->
<script setup lang="ts">
import type { PaginationProps, TableProps } from 'ant-design-vue'
import { pageApi, downloadExcelApi } from '~/api/clinics_wm/deptstock.ts'
import { listApi as findAllDeptLsApi,splitPackApi } from "~/api/clinics_wm/deptcustmap";
import help from "~/utils/help";
// import {findStockEnabledAllApi} from "~/api/hip/cattype.ts";
import BatchExpiryUpdate from '@/pages/clinics-wm/comp/batch-expiry-update.vue'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
import { Modal, message } from 'ant-design-vue'


const userStore = useUserStore()
// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[],
  pagination: any
}
const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品名',
    dataIndex: 'artName',
    width: 180,
    ellipsis: true,
    align: 'left',
    fixed: 'left'
  }, {
    title: '规格',
    dataIndex: 'artSpec',
    ellipsis: true,
    width: 150,
    align: 'left'
  }, {
    title: '厂家',
    dataIndex: 'producer',
    ellipsis: true,
    width: 250,
    align: 'left'
  }, {
    title: '整包单位',
    dataIndex: 'packUnit',
    width: 120,
    align: 'center'
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 120,
    align: 'right'
  }, {
    title: '整包成本单价',
    dataIndex: 'packPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零成本单价',
    dataIndex: 'cellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '加成比例',
    dataIndex: 'pctAdd',
    width: 120,
    align: 'right'
  }, {
    title: '整包单销售价',
    dataIndex: 'salePackPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零销售单价',
    dataIndex: 'saleCellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    width: 120,
    align: 'right'
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 120,
    align: 'right'
  }, {
    title: '临期天数',
    dataIndex: 'expiryDays',
    width: 120,
    align: 'right'
  }, {
    title: '批准文号',
    dataIndex: 'approvalNo',
    width: 180,
    align: 'left'
  }, {
    title: '允许拆零',
    dataIndex: 'splittable',
    width: 80,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      return text ? text === 1 ? '是' : '' : ''
    },
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 240,
    align: 'left'
  }, {
    title: '包装材质',
    dataIndex: 'packMaterial',
    width: 150,
    ellipsis: true,
    align: 'left'
  }, {
    title: '剂型名称',
    dataIndex: 'dosageForm',
    width: 120,
    align: 'left'
  // }, {
  //   title: '包装单位',
  //   dataIndex: 'packUnit',
  //   width: 120,
  //   align: 'left'
  // }, {
  //   title: '制剂单位',
  //   dataIndex: 'cellUnit',
  //   width: 120,
  //   align: 'left'
  }, {
    title: '货位编码',
    dataIndex: 'rackNo',
    width: 90,
    align: 'center'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 120,
    align: 'right'
  }, {
    title: '成本总额',
    dataIndex: 'costAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '销售总额',
    dataIndex: 'saleAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '仓库总库存',
    dataIndex: 'deptTotalPacks',
    width: 100,
    align: 'right',
    fixed: 'right'
  }, {
    title: '批次库存',
    dataIndex: 'currentTotal',
    width: 100,
    align: 'right',
    fixed: 'right'
  },{
    title: '操作',
    dataIndex: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }],
  dataSource: [],
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const {data} = await pageApi({
        ...searchFormModel,
        sidx: 't_article.Art_ID, t_dept_stock.stock_no',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0
      tableModel.sumRow = data.extra
      tableModel.loading = false
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${ tableModel.pagination.total } 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<any>({
})

const onReset = async () => {
  searchFormModel.S_EQ_t_article__art_id = undefined
  searchFormModel.expiryDays = undefined
  searchFormModel.keyword = undefined
  searchFormModel.S_EQ_t_article__cat_type_id = undefined
  searchFormModel.S_EQ_t_article__Subtype_ID = undefined
  searchFormModel.cellsTotalLe = undefined
  await onSearch()
}
const onSearch = async () => {
  tableModel.pagination.current = 1
  tableModel.loadDataSource()
}
const deptLs = ref<any[]>([])
const onChangeDept = () => {
  onSearch()
}
const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isOperator: 1
  })
  deptLs.value = data
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode
    onChangeDept()
  }
}
// const catTypeList = ref([])
// const getCatTypeLs = async () => {
//   const { data } = await findStockEnabledAllApi({})
//   catTypeList.value = data
// }
onMounted(async () => {
  await getDeptLs()
  // await getCatTypeLs()
  await onSearch()
})
const onDownloadExcel = async () => {
  const res: any = await downloadExcelApi({
    ...searchFormModel
  })
  help.expExcel(res, '库存明细数据')
}
const batchExpiryUpdateVisible = ref(false)
const batchExpiryUpdateRef = ref()
const handleEditBatch = (record) => {
  batchExpiryUpdateVisible.value = true
  batchExpiryUpdateRef.value?.init(record)
}

// 拆零盒整相关状态
const splitPackModalVisible = ref(false)
const currentSplitRecord = ref<any>(null)
const splitPackForm = reactive({
  deptMaxPacks: 0,    // 仓库总库存能合并的最大包装数
  batchMaxPacks: 0,   // 批次库存能合并的最大包装数
  deptInputPacks: 0,  // 用户输入的仓库整包数
  batchInputPacks: 0, // 用户输入的批次整包数
  deptInputCells: 0,  // 用户输入的仓库拆零数
  batchInputCells: 0, // 用户输入的批次拆零数
  deptMaxCells: 0,    // 仓库总库存最大拆零数
  batchMaxCells: 0,   // 批次库存最大拆零数
})

// 计算能合并的最大包装数和拆零数
const calculateMaxPackCount = (record: any) => {
  const packCells = record.packCells || 1

  // 计算仓库总库存
  const deptTotalPacks = record.deptTotalPacks || 0
  const deptTotalCells = record.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 仓库总库存的最大包装数和剩余拆零数
  splitPackForm.deptMaxPacks = Math.floor(deptTotalInCells / packCells)
  splitPackForm.deptMaxCells = deptTotalInCells % packCells

  // 计算批次库存
  const batchTotalPacks = record.totalPacks || 0
  const batchTotalCells = record.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 批次库存的最大包装数和剩余拆零数
  splitPackForm.batchMaxPacks = Math.floor(batchTotalInCells / packCells)
  splitPackForm.batchMaxCells = batchTotalInCells % packCells

  // 默认设置为原始数据（record传输的数据）
  splitPackForm.deptInputPacks = deptTotalPacks
  splitPackForm.batchInputPacks = batchTotalPacks
  splitPackForm.deptInputCells = deptTotalCells
  splitPackForm.batchInputCells = batchTotalCells
}

// 仓库总库存整包数变化时的联动计算
const handleDeptPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = deptTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.deptInputCells = remainingCells
  }
}

// 仓库总库存拆零数变化时的联动计算
const handleDeptCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const deptTotalPacks = currentSplitRecord.value.deptTotalPacks || 0
  const deptTotalCells = currentSplitRecord.value.deptTotalCells || 0
  const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > deptTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.deptInputCells = deptTotalInCells
    splitPackForm.deptInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.deptInputCells = 0
    splitPackForm.deptInputPacks = Math.floor(deptTotalInCells / packCells)
    return
  }

  // 智能计算整包数：
  // 1. 如果拆零数小于包装规格，尽可能多的分配整包
  // 2. 如果拆零数大于等于包装规格，优先保证拆零数，剩余分配整包

  if (numValue < packCells) {
    // 拆零数小于包装规格，优先分配整包
    const maxPossiblePacks = Math.floor((deptTotalInCells - numValue) / packCells)
    splitPackForm.deptInputPacks = maxPossiblePacks
  } else {
    // 拆零数大于等于包装规格，可以考虑转换部分为整包
    // 但保持用户输入的拆零数不变，只计算剩余能分配的整包数
    const remainingCellsForPacks = deptTotalInCells - numValue
    if (remainingCellsForPacks >= 0) {
      const calculatedPacks = Math.floor(remainingCellsForPacks / packCells)
      splitPackForm.deptInputPacks = Math.max(0, calculatedPacks)
    } else {
      splitPackForm.deptInputPacks = 0
    }
  }
}

// 批次库存整包数变化时的联动计算
const handleBatchPacksChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells

  // 计算剩余拆零数
  const usedCells = Number(value) * packCells
  const remainingCells = batchTotalInCells - usedCells

  if (remainingCells >= 0) {
    splitPackForm.batchInputCells = remainingCells
  }
}

// 批次库存拆零数变化时的联动计算
const handleBatchCellsChange = (value: any) => {
  if (!currentSplitRecord.value || value === null || value === undefined) return

  const packCells = currentSplitRecord.value.packCells || 1
  const batchTotalPacks = currentSplitRecord.value.totalPacks || 0
  const batchTotalCells = currentSplitRecord.value.totalCells || 0
  const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells
  const numValue = Number(value)

  // 验证拆零数不能超过原始总库存
  if (numValue > batchTotalInCells) {
    message.warning('拆零数不能超过原始总库存')
    splitPackForm.batchInputCells = batchTotalInCells
    splitPackForm.batchInputPacks = 0
    return
  }

  // 验证拆零数不能为负数
  if (numValue < 0) {
    splitPackForm.batchInputCells = 0
    splitPackForm.batchInputPacks = Math.floor(batchTotalInCells / packCells)
    return
  }

  // 智能计算整包数：
  // 1. 如果拆零数小于包装规格，尽可能多的分配整包
  // 2. 如果拆零数大于等于包装规格，优先保证拆零数，剩余分配整包

  if (numValue < packCells) {
    // 拆零数小于包装规格，优先分配整包
    const maxPossiblePacks = Math.floor((batchTotalInCells - numValue) / packCells)
    splitPackForm.batchInputPacks = maxPossiblePacks
  } else {
    // 拆零数大于等于包装规格，可以考虑转换部分为整包
    // 但保持用户输入的拆零数不变，只计算剩余能分配的整包数
    const remainingCellsForPacks = batchTotalInCells - numValue
    if (remainingCellsForPacks >= 0) {
      const calculatedPacks = Math.floor(remainingCellsForPacks / packCells)
      splitPackForm.batchInputPacks = Math.max(0, calculatedPacks)
    } else {
      splitPackForm.batchInputPacks = 0
    }
  }
}

// 拆零盒整方法
const handleSplitPack = (record) => {
  // 打开拆零盒整录入页面
  splitPackModalVisible.value = true
  currentSplitRecord.value = record

  // 计算能合并的最大包装数
  calculateMaxPackCount(record)
}

// 确认拆零盒整操作
const handleSplitPackConfirm = () => {
  if (!currentSplitRecord.value) return

  // 执行拆零盒整操作
  performSplitPackWithInput(currentSplitRecord.value)

  // 关闭模态框
  splitPackModalVisible.value = false
}

// 根据用户输入执行拆零盒整操作
const performSplitPackWithInput = async (record: any) => {
  try {
    // 验证输入数据
    const packCells = record.packCells || 1

    // 验证仓库总库存输入
    const deptTotalInCells = splitPackForm.deptInputPacks * packCells + splitPackForm.deptInputCells
    const deptOriginalTotalInCells = (record.deptTotalPacks || 0) * packCells + (record.deptTotalCells || 0)

    if (deptTotalInCells > deptOriginalTotalInCells) {
      message.error('仓库总库存输入超出原有库存总量')
      return
    }

    // 验证批次库存输入
    const batchTotalInCells = splitPackForm.batchInputPacks * packCells + splitPackForm.batchInputCells
    const batchOriginalTotalInCells = (record.totalPacks || 0) * packCells + (record.totalCells || 0)

    if (batchTotalInCells > batchOriginalTotalInCells) {
      message.error('批次库存输入超出原有库存总量')
      return
    }

    // 更新选中行的数据
    const index = tableModel.dataSource.findIndex(item =>
      item.artId === record.artId && item.stockNo === record.stockNo
    )

    if (index !== -1) {
      // 更新仓库总库存
      tableModel.dataSource[index].deptTotalPacks = splitPackForm.deptInputPacks
      tableModel.dataSource[index].deptTotalCells = splitPackForm.deptInputCells

      // 更新批次库存
      tableModel.dataSource[index].totalPacks = splitPackForm.batchInputPacks
      tableModel.dataSource[index].totalCells = splitPackForm.batchInputCells

      const deptDisplay = splitPackForm.deptInputPacks > 0 ?
        `${splitPackForm.deptInputPacks}${record.packUnit}` +
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '') :
        (splitPackForm.deptInputCells > 0 ? `${splitPackForm.deptInputCells}${record.cellUnit}` : '0')

      const batchDisplay = splitPackForm.batchInputPacks > 0 ?
        `${splitPackForm.batchInputPacks}${record.packUnit}` +
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '') :
        (splitPackForm.batchInputCells > 0 ? `${splitPackForm.batchInputCells}${record.cellUnit}` : '0')

      message.success(`拆零盒整完成！
仓库总库存：${deptDisplay}
批次库存：${batchDisplay}`)
    }
  } catch (error) {
    console.error('拆零盒整操作失败:', error)
    message.error('拆零盒整操作失败，请重试')
  }
}

// 取消拆零盒整操作
const handleSplitPackCancel = () => {
  splitPackModalVisible.value = false
  currentSplitRecord.value = null
}

// 执行拆零盒整操作
const performSplitPack = async (record) => {
  try {
   let printRecord = JSON.parse(JSON.stringify(record));
    console.log('拆零盒整操作开始:', JSON.stringify(printRecord, null, 2))
    // 获取整包单位数量
    const {data} = await splitPackApi({
        ...searchFormModel,
       record:JSON.stringify(printRecord, null, 2)
      })

    const packCells = record.packCells || 1

    // 1. 计算仓库总库存的总拆零数
    // 仓库总库存 = 整包数 * 整包单位 + 拆零数
    const deptTotalPacks = record.deptTotalPacks || 0 // 仓库总库存整包数
    const deptTotalCells = record.deptTotalCells || 0 // 仓库总库存拆零数
    const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells // 转换为总拆零数

    // 重新计算仓库总库存的盒整
    const deptNewPacks = Math.floor(deptTotalInCells / packCells) // 整包数量（取整）
    const deptNewCells = deptTotalInCells % packCells // 剩余拆零数量（取余）

    // 2. 计算批次库存的总拆零数
    // 批次库存 = 整包数 * 整包单位 + 拆零数
    const batchTotalPacks = record.totalPacks || 0 // 批次库存整包数
    const batchTotalCells = record.totalCells || 0 // 批次库存拆零数
    const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells // 转换为总拆零数

    // 重新计算批次库存的盒整
    const batchNewPacks = Math.floor(batchTotalInCells / packCells) // 整包数量（取整）
    const batchNewCells = batchTotalInCells % packCells // 剩余拆零数量（取余）

    // 3. 更新选中行的数据
    const index = tableModel.dataSource.findIndex(item =>
      item.artId === record.artId && item.stockNo === record.stockNo
    )

    if (index !== -1) {
      // 更新仓库总库存
      tableModel.dataSource[index].deptTotalPacks = deptNewPacks
      tableModel.dataSource[index].deptTotalCells = deptNewCells

      // 更新批次库存
      tableModel.dataSource[index].totalPacks = batchNewPacks
      tableModel.dataSource[index].totalCells = batchNewCells

//       message.success(`拆零盒整完成！
// 仓库总库存：${deptNewPacks}${record.packUnit}${deptNewCells > 0 ? deptNewCells + record.cellUnit : ''}
// 批次库存：${batchNewPacks}${record.packUnit}${batchNewCells > 0 ? batchNewCells + record.cellUnit : ''}`)

//       console.log('拆零盒整结果:', {
//         品种: record.artName,
//         整包单位: packCells,
//         仓库总库存: {
//           原始整包数: deptTotalPacks,
//           原始拆零数: deptTotalCells,
//           总拆零数: deptTotalInCells,
//           新整包数: deptNewPacks,
//           新拆零数: deptNewCells
//         },
//         批次库存: {
//           原始整包数: batchTotalPacks,
//           原始拆零数: batchTotalCells,
//           总拆零数: batchTotalInCells,
//           新整包数: batchNewPacks,
//           新拆零数: batchNewCells
//         }
//       })
    }
  } catch (error) {
    console.error('拆零盒整操作失败:', error)
    message.error('拆零盒整操作失败，请重试')
  }
}

const onCloseForm = () => {
  batchExpiryUpdateVisible.value = false
  tableModel.loadDataSource()
}
const tableSummary = computed(() => {
  if (!tableModel.sumRow) {
    console.log('数据未加载，跳过计算');
    return null;
  }
  return [{
    index: 0,
    colSpan: 1,
    label: '合计：',
    style: {}
  }, {
    index: 22,
    colSpan: 1,
    label: tableModel.sumRow.costAmount,
    style: {
      textAlign: 'right'
    }
  }, {
    index: 23,
    colSpan: 1,
    label: tableModel.sumRow.saleAmount,
    style: {
      textAlign: 'right'
    }
  }]
})
</script>
<template>
  <page-container>
    <div class="bg-fff box-shadow p-16px h-full">
      <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource" :rowKey="(item: any) => item.artId + '-' + item.stockNo"
                  :pagination="tableModel.pagination" :summary="tableSummary">
        <template #btns>
          <a-form :model="searchFormModel" w-full>
            <a-row p-t-2>
              <a-col flex="200px">
                <a-form-item label="仓库" name="deptCode">
                  <a-select v-model:value="searchFormModel.deptCode" placeholder="请选择仓库" style="min-width: 120px;" @change="onSearch">
                    <a-select-option v-for="item in deptLs" :key="item.deptCode" :value="item.deptCode">{{ item.deptName }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="临期天数">
                  <a-input-number v-model:value="searchFormModel.expiryDays" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="条目ID" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <a-input v-model:value="searchFormModel.S_EQ_t_article__art_id" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="250px">
                <a-form-item label="品名" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                  <a-input v-model:value="searchFormModel.keyword" allow-clear @pressEnter="onSearch"/>
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <StockReqCat v-model="searchFormModel.S_EQ_t_article__cat_type_id" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目亚类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <ArtSubTypeDict v-model="searchFormModel.S_EQ_t_article__Subtype_ID" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-button type="primary" :loading="tableModel.loading" @click="onSearch" m-l-2>
                  查询
                </a-button>
                <a-button @click="onReset" m-l-2>
                  重置
                </a-button>
              </a-col>
              <a-col flex="auto">
              </a-col>
              <a-col flex="100px">
                <a-button :loading="tableModel.loading" @click="onDownloadExcel">
                  导出
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
<!--          <template v-if="column?.dataIndex === 'artName'">-->
<!--            {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}-->
<!--          </template>-->
          <template v-if="column?.dataIndex === 'packUnit'">
            {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
          </template>
          <template v-if="column?.dataIndex === 'deptTotalPacks'">
            <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span><span v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'currentTotal'">
            <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'safeTotal'">
            <span v-if="record.safeTotalPacks">{{ record.safeTotalPacks }}{{ record.packUnit }}</span><span v-if="record.safeTotalCells">{{ record.safeTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'lastRestCells'">
            <span v-if="record.lastRestTotalPacks">{{ record.lastRestTotalPacks }}{{ record.packUnit }}</span><span v-if="record.lastRestTotalCells">{{ record.lastRestTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'action'">
             <a-space>
              <a @click="handleSplitPack(record)">拆零盒整</a>
            </a-space>
            &nbsp;
            <a-space>
              <a @click="handleEditBatch(record)">修改效期</a>
            </a-space>

          </template>
        </template>
      </base-table>
    </div>
    <batch-expiry-update ref="batchExpiryUpdateRef" v-model:visible="batchExpiryUpdateVisible" width="800px" @close="onCloseForm"/>

    <!-- 拆零盒整录入模态框 -->
    <a-modal
      v-model:open="splitPackModalVisible"
      title="拆零盒整录入"
      width="600px"
      @ok="handleSplitPackConfirm"
      @cancel="handleSplitPackCancel"
    >
      <div v-if="currentSplitRecord">
        <div class="mb-16px">
          <h4>品种信息</h4>
          <p><strong>品名：</strong>{{ currentSplitRecord.artName }}</p>
          <p><strong>规格：</strong>{{ currentSplitRecord.artSpec }}</p>
          <p><strong>生产厂家：</strong>{{ currentSplitRecord.producer }}</p>
          <p><strong>包装规格：</strong>{{ currentSplitRecord.packCells }}{{ currentSplitRecord.cellUnit }}/{{ currentSplitRecord.packUnit }}</p>
        </div>

        <a-divider />

        <a-form :model="splitPackForm" layout="vertical">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="仓库总库存整包数">
                <a-input-number
                  v-model:value="splitPackForm.deptInputPacks"
                  :min="0"
                  :max="splitPackForm.deptMaxPacks"
                  :addon-after="currentSplitRecord.packUnit"
                  style="width: 100%"
                  @change="handleDeptPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  最大：{{ splitPackForm.deptMaxPacks }}{{ currentSplitRecord.packUnit }}
                  (原始：{{ currentSplitRecord.deptTotalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="仓库总库存拆零数">
                <a-input-number
                  v-model:value="splitPackForm.deptInputCells"
                  :min="0"
                  :max="currentSplitRecord.packCells - 1"
                  :addon-after="currentSplitRecord.cellUnit"
                  style="width: 100%"
                  @change="handleDeptCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  范围：0-{{ currentSplitRecord.packCells - 1 }}{{ currentSplitRecord.cellUnit }}
                  (原始：{{ currentSplitRecord.deptTotalCells }}{{ currentSplitRecord.cellUnit }})
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="批次库存整包数">
                <a-input-number
                  v-model:value="splitPackForm.batchInputPacks"
                  :min="0"
                  :max="splitPackForm.batchMaxPacks"
                  :addon-after="currentSplitRecord.packUnit"
                  style="width: 100%"
                  @change="handleBatchPacksChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  最大：{{ splitPackForm.batchMaxPacks }}{{ currentSplitRecord.packUnit }}
                  (原始：{{ currentSplitRecord.totalPacks }}{{ currentSplitRecord.packUnit }})
                </div>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="批次库存拆零数">
                <a-input-number
                  v-model:value="splitPackForm.batchInputCells"
                  :min="0"
                  :max="currentSplitRecord.packCells - 1"
                  :addon-after="currentSplitRecord.cellUnit"
                  style="width: 100%"
                  @change="handleBatchCellsChange"
                />
                <div class="text-gray-500 text-sm mt-1">
                  范围：0-{{ currentSplitRecord.packCells - 1 }}{{ currentSplitRecord.cellUnit }}
                  (原始：{{ currentSplitRecord.totalCells }}{{ currentSplitRecord.cellUnit }})
                </div>
              </a-form-item>
            </a-col>
          </a-row>

          <a-divider />

          <div class="bg-gray-50 p-12px rounded">
            <h5>操作说明：</h5>
            <ul class="text-sm text-gray-600">
              <li>• <strong>智能联动计算：</strong></li>
              <li>&nbsp;&nbsp;- 输入整包数 → 自动计算剩余拆零数</li>
              <li>&nbsp;&nbsp;- 输入拆零数 → 智能计算最优整包数分配</li>
              <li>• <strong>智能分配策略：</strong></li>
              <li>&nbsp;&nbsp;- 拆零数 < 包装规格：优先分配整包，剩余为拆零</li>
              <li>&nbsp;&nbsp;- 拆零数 ≥ 包装规格：保持拆零数，剩余分配整包</li>
              <li>• 总量始终等于原始库存总量</li>
              <li>• 系统自动防止超量和负数输入</li>
            </ul>
          </div>
        </a-form>
      </div>
    </a-modal>
  </page-container>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
