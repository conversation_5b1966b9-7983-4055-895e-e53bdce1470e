<!--药库-库房业务-批号库存查询-->
<script setup lang="ts">
import type { PaginationProps, TableProps } from 'ant-design-vue'
import { pageApi, downloadExcelApi } from '~/api/clinics_wm/deptstock.ts'
import { listApi as findAllDeptLsApi,splitPackApi } from "~/api/clinics_wm/deptcustmap";
import help from "~/utils/help";
// import {findStockEnabledAllApi} from "~/api/hip/cattype.ts";
import BatchExpiryUpdate from '@/pages/clinics-wm/comp/batch-expiry-update.vue'
import { StockReqCat } from '@mh-hip/art-cat'
import { ArtSubTypeDict } from '@mh-hip/art-sub-type'
import { Modal, message } from 'ant-design-vue'


const userStore = useUserStore()
// 加载数据
interface TableModel {
  loading?: Boolean,
  columns: any[],
  dataSource: any[],
  selectedRowKeys: any[],
  pagination: any
}
const tableModel = reactive<TableModel>({
  loading: false,
  columns: [{
    title: '序号',
    dataIndex: 'index',
    width: 60,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品种ID',
    dataIndex: 'artId',
    width: 80,
    align: 'center',
    fixed: 'left'
  }, {
    title: '品名',
    dataIndex: 'artName',
    width: 180,
    ellipsis: true,
    align: 'left',
    fixed: 'left'
  }, {
    title: '规格',
    dataIndex: 'artSpec',
    ellipsis: true,
    width: 150,
    align: 'left'
  }, {
    title: '厂家',
    dataIndex: 'producer',
    ellipsis: true,
    width: 250,
    align: 'left'
  }, {
    title: '整包单位',
    dataIndex: 'packUnit',
    width: 120,
    align: 'center'
  }, {
    title: '批号',
    dataIndex: 'batchNo',
    width: 120,
    align: 'right'
  }, {
    title: '整包成本单价',
    dataIndex: 'packPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零成本单价',
    dataIndex: 'cellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '加成比例',
    dataIndex: 'pctAdd',
    width: 120,
    align: 'right'
  }, {
    title: '整包单销售价',
    dataIndex: 'salePackPrice',
    width: 120,
    align: 'right'
  }, {
    title: '拆零销售单价',
    dataIndex: 'saleCellPrice',
    width: 120,
    align: 'right'
  }, {
    title: '生产日期',
    dataIndex: 'dateManufactured',
    width: 120,
    align: 'right'
  }, {
    title: '有效期至',
    dataIndex: 'expiry',
    width: 120,
    align: 'right'
  }, {
    title: '临期天数',
    dataIndex: 'expiryDays',
    width: 120,
    align: 'right'
  }, {
    title: '批准文号',
    dataIndex: 'approvalNo',
    width: 180,
    align: 'left'
  }, {
    title: '允许拆零',
    dataIndex: 'splittable',
    width: 80,
    align: 'center',
    customRender: ({ text }: { text: number }) => {
      return text ? text === 1 ? '是' : '' : ''
    },
  }, {
    title: '医保编码',
    dataIndex: 'miCode',
    width: 240,
    align: 'left'
  }, {
    title: '包装材质',
    dataIndex: 'packMaterial',
    width: 150,
    ellipsis: true,
    align: 'left'
  }, {
    title: '剂型名称',
    dataIndex: 'dosageForm',
    width: 120,
    align: 'left'
  // }, {
  //   title: '包装单位',
  //   dataIndex: 'packUnit',
  //   width: 120,
  //   align: 'left'
  // }, {
  //   title: '制剂单位',
  //   dataIndex: 'cellUnit',
  //   width: 120,
  //   align: 'left'
  }, {
    title: '货位编码',
    dataIndex: 'rackNo',
    width: 90,
    align: 'center'
  }, {
    title: '批次',
    dataIndex: 'stockNo',
    width: 120,
    align: 'right'
  }, {
    title: '成本总额',
    dataIndex: 'costAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '销售总额',
    dataIndex: 'saleAmount',
    width: 120,
    align: 'right',
    fixed: 'right'
  }, {
    title: '仓库总库存',
    dataIndex: 'deptTotalPacks',
    width: 100,
    align: 'right',
    fixed: 'right'
  }, {
    title: '批次库存',
    dataIndex: 'currentTotal',
    width: 100,
    align: 'right',
    fixed: 'right'
  }, {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    align: 'center',
    fixed: 'right'
  }],
  dataSource: [],
  loadDataSource: async () => {
    tableModel.loading = true
    try {
      const {data} = await pageApi({
        ...searchFormModel,
        sidx: 't_article.Art_ID, t_dept_stock.stock_no',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
      tableModel.dataSource = data.list
      tableModel.pagination.total = data.total ?? 0
      tableModel.sumRow = data.extra
      tableModel.loading = false
    } catch (err) {
      console.log(err)
      tableModel.loading = false
    }
  },
  selectedRowKeys: [],
  pagination: reactive<PaginationProps>({
    pageSize: 10,
    current: 1,
    total: 0,
    showQuickJumper: true,
    showTotal: total => `共：${ tableModel.pagination.total } 条`,
    onChange(current, pageSize) {
      tableModel.pagination.pageSize = pageSize
      tableModel.pagination.current = current
      tableModel.loadDataSource()
    },
  })
})

const searchFormModel = reactive<any>({
})

const onReset = async () => {
  searchFormModel.S_EQ_t_article__art_id = undefined
  searchFormModel.expiryDays = undefined
  searchFormModel.keyword = undefined
  searchFormModel.S_EQ_t_article__cat_type_id = undefined
  searchFormModel.S_EQ_t_article__Subtype_ID = undefined
  searchFormModel.cellsTotalLe = undefined
  await onSearch()
}
const onSearch = async () => {
  tableModel.pagination.current = 1
  tableModel.loadDataSource()
}
const deptLs = ref<any[]>([])
const onChangeDept = () => {
  onSearch()
}
const getDeptLs = async () => {
  const { data } = await findAllDeptLsApi({
    isOperator: 1
  })
  deptLs.value = data
  if (data.length > 0) {
    searchFormModel.deptCode = data[0].deptCode
    onChangeDept()
  }
}
// const catTypeList = ref([])
// const getCatTypeLs = async () => {
//   const { data } = await findStockEnabledAllApi({})
//   catTypeList.value = data
// }
onMounted(async () => {
  await getDeptLs()
  // await getCatTypeLs()
  await onSearch()
})
const onDownloadExcel = async () => {
  const res: any = await downloadExcelApi({
    ...searchFormModel
  })
  help.expExcel(res, '库存明细数据')
}
const batchExpiryUpdateVisible = ref(false)
const batchExpiryUpdateRef = ref()
const handleEditBatch = (record) => {
  batchExpiryUpdateVisible.value = true
  batchExpiryUpdateRef.value?.init(record)
}

// 拆零盒整方法
const handleSplitPack = (record) => {
  Modal.confirm({
    title: '拆零盒整确认',
    content: `确定要对品种"${record.artName}"进行拆零盒整操作吗？此操作将自动计算并替换仓库总库存与批次库存数据。`,
    okText: '确定',
    cancelText: '取消',
    onOk() {
      performSplitPack(record)
    }
  })
}

// 执行拆零盒整操作
const performSplitPack = (record) => {
  try {
   let printRecord = JSON.parse(JSON.stringify(record));
    console.log('拆零盒整操作开始:', JSON.stringify(printRecord, null, 2))
    // 获取整包单位数量
    const {data} = await pageApi({
        ...searchFormModel,
        sidx: 't_article.Art_ID, t_dept_stock.stock_no',
        order: 'asc',
        pageNum: tableModel.pagination.current,
        pageSize: tableModel.pagination.pageSize,
      })
    const packCells = record.packCells || 1

    // 1. 计算仓库总库存的总拆零数
    // 仓库总库存 = 整包数 * 整包单位 + 拆零数
    const deptTotalPacks = record.deptTotalPacks || 0 // 仓库总库存整包数
    const deptTotalCells = record.deptTotalCells || 0 // 仓库总库存拆零数
    const deptTotalInCells = deptTotalPacks * packCells + deptTotalCells // 转换为总拆零数

    // 重新计算仓库总库存的盒整
    const deptNewPacks = Math.floor(deptTotalInCells / packCells) // 整包数量（取整）
    const deptNewCells = deptTotalInCells % packCells // 剩余拆零数量（取余）

    // 2. 计算批次库存的总拆零数
    // 批次库存 = 整包数 * 整包单位 + 拆零数
    const batchTotalPacks = record.totalPacks || 0 // 批次库存整包数
    const batchTotalCells = record.totalCells || 0 // 批次库存拆零数
    const batchTotalInCells = batchTotalPacks * packCells + batchTotalCells // 转换为总拆零数

    // 重新计算批次库存的盒整
    const batchNewPacks = Math.floor(batchTotalInCells / packCells) // 整包数量（取整）
    const batchNewCells = batchTotalInCells % packCells // 剩余拆零数量（取余）

    // 3. 更新选中行的数据
    const index = tableModel.dataSource.findIndex(item =>
      item.artId === record.artId && item.stockNo === record.stockNo
    )

    if (index !== -1) {
      // 更新仓库总库存
      tableModel.dataSource[index].deptTotalPacks = deptNewPacks
      tableModel.dataSource[index].deptTotalCells = deptNewCells

      // 更新批次库存
      tableModel.dataSource[index].totalPacks = batchNewPacks
      tableModel.dataSource[index].totalCells = batchNewCells

//       message.success(`拆零盒整完成！
// 仓库总库存：${deptNewPacks}${record.packUnit}${deptNewCells > 0 ? deptNewCells + record.cellUnit : ''}
// 批次库存：${batchNewPacks}${record.packUnit}${batchNewCells > 0 ? batchNewCells + record.cellUnit : ''}`)

//       console.log('拆零盒整结果:', {
//         品种: record.artName,
//         整包单位: packCells,
//         仓库总库存: {
//           原始整包数: deptTotalPacks,
//           原始拆零数: deptTotalCells,
//           总拆零数: deptTotalInCells,
//           新整包数: deptNewPacks,
//           新拆零数: deptNewCells
//         },
//         批次库存: {
//           原始整包数: batchTotalPacks,
//           原始拆零数: batchTotalCells,
//           总拆零数: batchTotalInCells,
//           新整包数: batchNewPacks,
//           新拆零数: batchNewCells
//         }
//       })
    }
  } catch (error) {
    console.error('拆零盒整操作失败:', error)
    message.error('拆零盒整操作失败，请重试')
  }
}

const onCloseForm = () => {
  batchExpiryUpdateVisible.value = false
  tableModel.loadDataSource()
}
const tableSummary = computed(() => {
  if (!tableModel.sumRow) {
    console.log('数据未加载，跳过计算');
    return null;
  }
  return [{
    index: 0,
    colSpan: 1,
    label: '合计：',
    style: {}
  }, {
    index: 22,
    colSpan: 1,
    label: tableModel.sumRow.costAmount,
    style: {
      textAlign: 'right'
    }
  }, {
    index: 23,
    colSpan: 1,
    label: tableModel.sumRow.saleAmount,
    style: {
      textAlign: 'right'
    }
  }]
})
</script>
<template>
  <page-container>
    <div class="bg-fff box-shadow p-16px h-full">
      <base-table :loading="tableModel.loading" :columns="tableModel.columns" :dataSource="tableModel.dataSource" :rowKey="(item: any) => item.artId + '-' + item.stockNo"
                  :pagination="tableModel.pagination" :summary="tableSummary">
        <template #btns>
          <a-form :model="searchFormModel" w-full>
            <a-row p-t-2>
              <a-col flex="200px">
                <a-form-item label="仓库" name="deptCode">
                  <a-select v-model:value="searchFormModel.deptCode" placeholder="请选择仓库" style="min-width: 120px;" @change="onSearch">
                    <a-select-option v-for="item in deptLs" :key="item.deptCode" :value="item.deptCode">{{ item.deptName }}</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="临期天数">
                  <a-input-number v-model:value="searchFormModel.expiryDays" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-form-item label="条目ID" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
                  <a-input v-model:value="searchFormModel.S_EQ_t_article__art_id" allow-clear @pressEnter="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="250px">
                <a-form-item label="品名" :label-col="{ span: 4 }" :wrapper-col="{ span: 19 }">
                  <a-input v-model:value="searchFormModel.keyword" allow-clear @pressEnter="onSearch"/>
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目分类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <StockReqCat v-model="searchFormModel.S_EQ_t_article__cat_type_id" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="200px">
                <a-form-item label="条目亚类" :label-col="{ span: 10 }" :wrapper-col="{ span: 14 }">
                  <ArtSubTypeDict v-model="searchFormModel.S_EQ_t_article__Subtype_ID" type="Select" @change="onSearch" />
                </a-form-item>
              </a-col>
              <a-col flex="180px">
                <a-button type="primary" :loading="tableModel.loading" @click="onSearch" m-l-2>
                  查询
                </a-button>
                <a-button @click="onReset" m-l-2>
                  重置
                </a-button>
              </a-col>
              <a-col flex="auto">
              </a-col>
              <a-col flex="100px">
                <a-button :loading="tableModel.loading" @click="onDownloadExcel">
                  导出
                </a-button>
              </a-col>
            </a-row>
          </a-form>
        </template>
        <template #bodyCell="{ column, record, index }">
          <template v-if="column?.dataIndex === 'index'">
            {{ index + 1 }}
          </template>
<!--          <template v-if="column?.dataIndex === 'artName'">-->
<!--            {{ record.artName }} {{ record.artSpec }}<br/>{{ record.producer }}-->
<!--          </template>-->
          <template v-if="column?.dataIndex === 'packUnit'">
            {{ record.packCells }} {{ record.cellUnit }} / {{ record.packUnit }}
          </template>
          <template v-if="column?.dataIndex === 'deptTotalPacks'">
            <span v-if="record.deptTotalPacks">{{ record.deptTotalPacks }}{{ record.packUnit }}</span><span v-if="record.deptTotalCells">{{ record.deptTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'currentTotal'">
            <span v-if="record.totalPacks">{{ record.totalPacks }}{{ record.packUnit }}</span><span v-if="record.totalCells">{{ record.totalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'safeTotal'">
            <span v-if="record.safeTotalPacks">{{ record.safeTotalPacks }}{{ record.packUnit }}</span><span v-if="record.safeTotalCells">{{ record.safeTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'lastRestCells'">
            <span v-if="record.lastRestTotalPacks">{{ record.lastRestTotalPacks }}{{ record.packUnit }}</span><span v-if="record.lastRestTotalCells">{{ record.lastRestTotalCells }}{{ record.cellUnit }}</span>
          </template>
          <template v-if="column?.dataIndex === 'action'">
             <a-space>
              <a @click="handleSplitPack(record)">拆零盒整</a>
            </a-space>
            &nbsp;
            <a-space>
              <a @click="handleEditBatch(record)">修改效期</a>
            </a-space>

          </template>
        </template>
      </base-table>
    </div>
    <batch-expiry-update ref="batchExpiryUpdateRef" v-model:visible="batchExpiryUpdateVisible" width="800px" @close="onCloseForm"/>
  </page-container>
</template>
<style lang="less" scoped>
.bg-fff {
  background-color: #fff;
  // padding: 8px 0 0 8px;

  :deep(.ant-tabs-nav) {
    margin-bottom: 0;
  }
  :deep(.ant-tabs-nav-list) {
    margin-left: 10px;
  }
}
</style>
