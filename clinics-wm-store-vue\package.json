{"name": "antdv-pro", "type": "module", "version": "1.1.0", "packageManager": "pnpm@8.10.0", "author": "aibayanyu <<EMAIL>>", "repository": "antdv-pro/antdv-pro", "engines": {"node": ">=18.15.0"}, "scripts": {"dev": "vite", "dev_oy": "vite --mode dev_oy", "build": "vue-tsc --noEmit --skipLibCheck && vite build --mode production", "build:pre": "vite build --mode production", "build:preview": "vite build --mode production_preview", "clear:vercel": "rm -rf ./vercel.json", "build:vercel": "run-s clear:vercel build:nitro", "build:nitro": "mist build nitro", "start:nirto": "node .output/server/index.mjs", "preview": "mist preview", "lint": "eslint src --fix", "typecheck": "vue-tsc --noEmit", "bump:patch": "changelogen --bump --output CHANGELOG.md --release", "bump:minor": "changelogen --bump --output CHANGELOG.md --release --minor", "bump:major": "changelogen --bump --output CHANGELOG.md --release --major", "prepare": "husky", "dir-tree": "esno ./scripts/dir-tree", "gen:uno": "esno ./scripts/gen-unocss", "toJS": "esno scripts/to-js"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2plot": "^2.4.33", "@antv/l7": "^2.22.5", "@ctrl/tinycolor": "^4.1.0", "@idmy/antd": "0.0.119", "@idmy/core": "^1.0.143", "@mh-base/core": "^1.0.24", "@mh-hip/art-cat": "^1.0.0", "@mh-hip/art-sub-type": "^1.0.0", "@mh-hip/util": "^1.0.6", "@mh-inpatient-hsd/selector": "^1.0.10", "@mh-wm/batch-adjust": "^1.0.4", "@mh-wm/count": "^1.0.6", "@mh-wm/pharmacy": "^1.0.1", "@mh-wm/req-component": "^1.0.7", "@mh-wm/maker": "^1.0.22", "@mh-wm/scm-cust": "^1.0.8", "@mh-wm/util": "^1.0.12", "@surely-vue/table": "~5.0.3", "@types/crypto-js": "^4.2.2", "@v-c/utils": "^0.0.26", "@vueuse/core": "^13.2.0", "ant-design-vue": "^4.2.6", "axios": "^1.9.0", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "lodash-es": "^4.17.21", "med-base-ui": "1.3.118", "mitt": "^3.0.1", "pinia": "^3.0.2", "vue": "^3.5.14", "vue-i18n": "^11.1.3", "vue-router": "^4.5.1", "vue3-cookies": "^1.0.6"}, "devDependencies": {"@antfu/eslint-config": "^4.13.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@mistjs/cli": "0.0.1-beta.9", "@mistjs/vite-plugin-preload": "^0.0.1", "@types/fs-extra": "^11.0.4", "@types/lodash": "^4.17.16", "@types/lodash-es": "^4.17.12", "@types/node": "^22.15.19", "@types/treeify": "^1.0.3", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/test-utils": "^2.4.6", "antdv-component-resolver": "^1.0.7", "antdv-style": "0.0.0", "changelogen": "^0.6.1", "cross-env": "^7.0.3", "directory-tree": "^3.5.2", "esbuild": "^0.25.4", "eslint": "^9.27.0", "esno": "^4.8.0", "execa": "^9.5.3", "fs-extra": "^11.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "less": "^4.3.0", "lint-staged": "^16.0.0", "lodash": "^4.17.21", "nitropack": "^2.11.12", "npm-run-all": "^4.1.5", "picocolors": "^1.1.1", "treeify": "^1.1.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "unocss": "^66.1.2", "unocss-preset-chinese": "^0.3.3", "unocss-preset-ease": "^0.0.4", "unplugin-auto-import": "^19.2.0", "unplugin-config": "^0.1.5", "unplugin-vue-components": "^28.5.0", "vite": "^6.3.5", "vite-plugin-compression2": "^1.3.3", "vitest": "^3.1.3", "vue-tsc": "^2.2.10"}, "lint-staged": {"**/*.{vue,ts,js,jsx,tsx}": "eslint src --fix"}}