import { getClinicsWmUrl } from '../common/base-api'

const api = {
  page: getClinicsWmUrl('/clinics_wm/deptcustmap/page'),
  save: getClinicsWmUrl('/clinics_wm/deptcustmap/save'),
  info: getClinicsWmUrl('/clinics_wm/deptcustmap/info'),
  delete: getClinicsWmUrl('/clinics_wm/deptcustmap/delete'),
  findAll: getClinicsWmUrl('/clinics_wm/deptcustmap/findAll'),
  list: getClinicsWmUrl('/clinics_wm/deptcustmap/list'),
  splitPack: getClinicsWmUrl('/clinics_wm/wmbill/performSplitPack')
}

export function pageApi(params: any) {
  return usePost(api.page, params)
}
export function findAllApi(params: any) {
  return usePost(api.findAll, params)
}
export function saveApi(params: any) {
  return usePost(api.save, params)
}
export function infoApi(params: any) {
  return usePost(api.info, params)
}
export function deleteApi(params: any) {
  return usePost(api.delete, params)
}
export function listApi(params: any) {
  return usePost(api.list, params)
}
export function splitPackApi(params: any) {
  return usePost(api.splitPack, params)
}
